<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>艾宾浩斯学习管理系统 - 完整项目分析</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .controls {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.secondary {
            background: #6c757d;
        }
        .btn.secondary:hover {
            background: #545b62;
        }
        .diagram-container {
            padding: 20px;
            text-align: center;
            min-height: 600px;
        }
        .mermaid {
            max-width: 100%;
            height: auto;
        }
        .zoom-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 10px;
        }
        .zoom-btn {
            display: block;
            width: 40px;
            height: 40px;
            margin: 5px 0;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
        }
        .zoom-btn:hover {
            background: #0056b3;
        }
        .info-panel {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px;
            border-radius: 4px;
        }
        .info-panel h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px;
        }
        .stat-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #6c757d;
            font-size: 14px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>艾宾浩斯记忆曲线学习管理系统</h1>
            <p>完整项目分析 - 交互式思维导图</p>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="showOverview()">概览视图</button>
            <button class="btn" onclick="showRiskAnalysis()">风险分析</button>
            <button class="btn" onclick="showCompetitorAnalysis()">竞品分析</button>
            <button class="btn" onclick="showBusinessModel()">商业模式</button>
            <button class="btn" onclick="showImplementation()">实施计划</button>
            <button class="btn secondary" onclick="resetZoom()">重置缩放</button>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">11</div>
                <div class="stat-label">主要分支</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">290</div>
                <div class="stat-label">总行数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">4</div>
                <div class="stat-label">新增维度</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">108</div>
                <div class="stat-label">新增内容行</div>
            </div>
        </div>

        <div class="info-panel">
            <h3>使用说明</h3>
            <p>• 点击上方按钮切换不同的分析视图</p>
            <p>• 使用右侧缩放按钮调整图表大小</p>
            <p>• 每个视图都经过优化，确保内容清晰可读</p>
            <p>• 建议按顺序查看：概览 → 风险 → 竞品 → 商业 → 实施</p>
        </div>

        <div class="diagram-container">
            <div id="mermaid-diagram" class="mermaid">
                <!-- 图表内容将在这里动态加载 -->
            </div>
        </div>
    </div>

    <div class="zoom-controls">
        <button class="zoom-btn" onclick="zoomIn()">+</button>
        <button class="zoom-btn" onclick="zoomOut()">-</button>
        <button class="zoom-btn" onclick="resetZoom()" style="font-size: 14px;">⌂</button>
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            themeVariables: {
                fontFamily: 'Microsoft YaHei, Arial, sans-serif',
                fontSize: '14px'
            }
        });

        let currentZoom = 1;
        const zoomStep = 0.2;
        const minZoom = 0.5;
        const maxZoom = 3;

        // 缩放功能
        function zoomIn() {
            if (currentZoom < maxZoom) {
                currentZoom += zoomStep;
                applyZoom();
            }
        }

        function zoomOut() {
            if (currentZoom > minZoom) {
                currentZoom -= zoomStep;
                applyZoom();
            }
        }

        function resetZoom() {
            currentZoom = 1;
            applyZoom();
        }

        function applyZoom() {
            const diagram = document.getElementById('mermaid-diagram');
            diagram.style.transform = `scale(${currentZoom})`;
            diagram.style.transformOrigin = 'center top';
        }

        // 显示不同的视图
        function showDiagram(diagramCode) {
            const container = document.getElementById('mermaid-diagram');
            container.innerHTML = diagramCode;
            mermaid.init(undefined, container);
        }

        // 概览视图
        function showOverview() {
            const overviewDiagram = `
graph TD
    A[艾宾浩斯记忆曲线学习管理系统<br/>完整项目分析] --> B[项目概述]
    A --> C[用户分析]
    A --> D[功能需求]
    A --> E[非功能需求]
    A --> F[验收标准]
    A --> G[管理规范]
    A --> H[技术约束]
    A --> I[风险分析]
    A --> J[竞品分析]
    A --> K[商业模式]
    A --> L[实施计划]

    B --> B1[项目背景]
    B --> B2[核心价值主张]
    B --> B3[项目目标]

    C --> C1[目标用户]
    C --> C2[用户场景]
    C --> C3[交互设计]

    D --> D1[P0核心功能<br/>6个必需功能]
    D --> D2[P1重要功能<br/>2个重要功能]
    D --> D3[P2有用功能<br/>2个有用功能]

    I --> I1[技术风险]
    I --> I2[市场风险]
    I --> I3[商业风险]
    I --> I4[项目风险]

    J --> J1[直接竞品]
    J --> J2[间接竞品]
    J --> J3[竞争优势]
    J --> J4[差异化策略]

    K --> K1[收入模式]
    K --> K2[成本结构]
    K --> K3[盈利预期]
    K --> K4[商业策略]

    L --> L1[开发阶段]
    L --> L2[里程碑]
    L --> L3[资源配置]
    L --> L4[时间规划]

    classDef projectBox fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef userBox fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef funcBox fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef riskBox fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef compBox fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef bizBox fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef planBox fill:#fff8e1,stroke:#f57c00,stroke-width:2px

    class B,B1,B2,B3 projectBox
    class C,C1,C2,C3 userBox
    class D,D1,D2,D3 funcBox
    class I,I1,I2,I3,I4 riskBox
    class J,J1,J2,J3,J4 compBox
    class K,K1,K2,K3,K4 bizBox
    class L,L1,L2,L3,L4 planBox
            `;
            showDiagram(overviewDiagram);
        }

        // 风险分析视图
        function showRiskAnalysis() {
            const riskDiagram = `
graph TD
    I[风险分析] --> I1[技术风险]
    I --> I2[市场风险]
    I --> I3[商业风险]
    I --> I4[项目风险]

    I1 --> I11[浏览器兼容性<br/>API支持差异<br/>性能差异<br/>兼容性问题]
    I1 --> I12[本地存储限制<br/>容量限制<br/>数据丢失风险<br/>同步困难]
    I1 --> I13[纯前端架构限制<br/>无法实时协作<br/>统计分析困难<br/>行为跟踪受限]
    I1 --> I14[算法实现复杂度<br/>艾宾浩斯调优<br/>负载均衡准确性<br/>时间预估精度]

    I2 --> I21[竞品威胁<br/>Anki等成熟产品<br/>新兴AI学习工具<br/>教育机构自研]
    I2 --> I22[用户接受度<br/>学习习惯改变<br/>家长信任度<br/>教师推荐意愿]
    I2 --> I23[市场规模<br/>目标用户有限<br/>付费意愿不确定<br/>教育成本高]

    I3 --> I31[盈利模式<br/>订阅接受度<br/>定价合理性<br/>转化率预期]
    I3 --> I32[成本控制<br/>开发成本超预期<br/>推广成本过高<br/>维护成本增长]
    I3 --> I33[现金流<br/>投入回收周期<br/>用户增长速度<br/>收入稳定性]

    I4 --> I41[开发进度<br/>复杂度超预期<br/>技术难点<br/>测试周期延长]
    I4 --> I42[团队资源<br/>人员流失<br/>技能匹配<br/>工作量分配]
    I4 --> I43[质量控制<br/>用户体验<br/>性能指标<br/>兼容性问题]
    I4 --> I44[需求变更<br/>理解偏差<br/>环境变化<br/>方案调整]

    classDef riskBox fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef techRisk fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef marketRisk fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef bizRisk fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef projectRisk fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px

    class I riskBox
    class I1,I11,I12,I13,I14 techRisk
    class I2,I21,I22,I23 marketRisk
    class I3,I31,I32,I33 bizRisk
    class I4,I41,I42,I43,I44 projectRisk
            `;
            showDiagram(riskDiagram);
        }

        // 竞品分析视图
        function showCompetitorAnalysis() {
            const compDiagram = `
graph TD
    J[竞品分析] --> J1[直接竞品]
    J --> J2[间接竞品]
    J --> J3[竞争优势]
    J --> J4[差异化策略]

    J1 --> J11[Anki<br/>成熟稳定，用户基数大<br/>界面陈旧，学习曲线陡<br/>免费+同步付费]
    J1 --> J12[SuperMemo<br/>算法先进，科学性强<br/>价格昂贵，操作复杂<br/>一次性高价购买]
    J1 --> J13[Quizlet<br/>社交功能，内容丰富<br/>缺乏科学复习算法<br/>免费+高级功能付费]

    J2 --> J21[Forest<br/>专注力管理工具<br/>游戏化设计优秀<br/>用户粘性强]
    J2 --> J22[番茄工作法应用<br/>时间管理工具<br/>简单易用<br/>广泛接受]
    J2 --> J23[思维导图工具<br/>知识整理工具<br/>可视化效果好<br/>学习辅助价值]

    J3 --> J31[科学算法<br/>艾宾浩斯记忆曲线<br/>个性化调整机制<br/>负载均衡优化]
    J3 --> J32[一体化设计<br/>任务+复习+思维导图<br/>学习全流程覆盖<br/>数据互通分析]
    J3 --> J33[离线优先<br/>纯前端架构<br/>隐私保护优秀<br/>无网络依赖]
    J3 --> J34[用户体验<br/>现代化界面设计<br/>5分钟上手目标<br/>桌面端优化]

    J4 --> J41[目标用户<br/>专注初中生群体<br/>家长付费模式<br/>教育场景优化]
    J4 --> J42[技术路线<br/>纯前端差异化<br/>离线优先策略<br/>隐私友好定位]
    J4 --> J43[功能定位<br/>学习管理而非记忆<br/>思维导图集成<br/>智能时间管理]

    classDef compBox fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef directComp fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef indirectComp fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef advantage fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef strategy fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px

    class J compBox
    class J1,J11,J12,J13 directComp
    class J2,J21,J22,J23 indirectComp
    class J3,J31,J32,J33,J34 advantage
    class J4,J41,J42,J43 strategy
            `;
            showDiagram(compDiagram);
        }

        // 商业模式视图
        function showBusinessModel() {
            const bizDiagram = `
graph TD
    K[商业模式] --> K1[收入模式]
    K --> K2[成本结构]
    K --> K3[盈利预期]
    K --> K4[商业策略]

    K1 --> K11[月度订阅<br/>基础版免费（限50任务）<br/>高级版15-25元/月<br/>年度订阅8折优惠]
    K1 --> K12[功能分层<br/>免费：基础任务管理<br/>付费：思维导图+分析<br/>高级：无限任务+导出]
    K1 --> K13[教育机构<br/>学校批量授权<br/>班级管理功能<br/>教师监控面板]
    K1 --> K14[激活码销售<br/>线下渠道销售<br/>礼品卡模式<br/>代理商分销]

    K2 --> K21[开发成本<br/>前端开发：2人×6个月<br/>UI设计：1人×3个月<br/>总计：约30万元]
    K2 --> K22[运营成本<br/>CDN服务：月500元<br/>推广费用：月5000元<br/>年运营：约7万元]
    K2 --> K23[维护成本<br/>功能迭代：1人×持续<br/>用户支持：0.5人<br/>年维护：约15万元]

    K3 --> K31[用户增长预测<br/>第1年：1000付费用户<br/>第2年：5000付费用户<br/>第3年：15000付费用户]
    K3 --> K32[收入预测<br/>第1年：24万元收入<br/>第2年：120万元收入<br/>第3年：360万元收入]
    K3 --> K33[盈利分析<br/>第1年：亏损6万元<br/>第2年：盈利98万元<br/>投资回收期：14个月]

    K4 --> K41[市场定位<br/>中高端教育工具<br/>科学学习方法<br/>家长愿意投资]
    K4 --> K42[推广策略<br/>教育博主合作<br/>家长群体营销<br/>学校试点推广]
    K4 --> K43[用户留存<br/>学习效果可视化<br/>家长监督功能<br/>学习成就系统]
    K4 --> K44[扩展计划<br/>多学科内容库<br/>AI智能推荐<br/>国际化版本]

    classDef bizBox fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef revenue fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef cost fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef profit fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef strategy fill:#e3f2fd,stroke:#1565c0,stroke-width:2px

    class K bizBox
    class K1,K11,K12,K13,K14 revenue
    class K2,K21,K22,K23 cost
    class K3,K31,K32,K33 profit
    class K4,K41,K42,K43,K44 strategy
            `;
            showDiagram(bizDiagram);
        }

        // 实施计划视图
        function showImplementation() {
            const implDiagram = `
graph TD
    L[实施计划] --> L1[开发阶段]
    L --> L2[里程碑]
    L --> L3[资源配置]
    L --> L4[时间规划]

    L1 --> L11[MVP阶段<br/>时间：3个月<br/>目标：P0核心功能<br/>团队：2前端+1设计]
    L1 --> L12[完整版阶段<br/>时间：3个月<br/>目标：P1P2功能完善<br/>团队：3前端+1测试]
    L1 --> L13[增强版阶段<br/>时间：6个月<br/>目标：高级功能+优化<br/>团队：4人+运营]

    L2 --> L21[需求确认<br/>第1个月<br/>完整需求文档<br/>用户访谈验证]
    L2 --> L22[技术验证<br/>第2个月<br/>技术原型<br/>核心算法验证]
    L2 --> L23[MVP发布<br/>第3个月<br/>基础功能产品<br/>用户测试通过]
    L2 --> L24[Beta测试<br/>第4个月<br/>完整功能产品<br/>100用户测试]
    L2 --> L25[正式发布<br/>第6个月<br/>商业化产品<br/>商业指标达成]
    L2 --> L26[规模化<br/>第12个月<br/>成熟产品<br/>盈利目标达成]

    L3 --> L31[开发团队<br/>前端工程师：2-4人<br/>UI/UX设计师：1人<br/>测试工程师：1人]
    L3 --> L32[技术资源<br/>开发工具：VS Code等<br/>设计工具：Figma<br/>部署：CDN+GitHub]
    L3 --> L33[运营资源<br/>推广预算：月5000元<br/>客服支持：兼职<br/>内容创作：外包]

    L4 --> L41[第1季度<br/>月1：需求分析+设计<br/>月2：技术架构+原型<br/>月3：核心功能开发]
    L4 --> L42[第2季度<br/>月4：功能完善+测试<br/>月5：用户体验优化<br/>月6：商业化准备]
    L4 --> L43[第3季度<br/>月7：市场推广<br/>月8：用户反馈优化<br/>月9：功能迭代]
    L4 --> L44[第4季度<br/>月10：高级功能开发<br/>月11：性能优化<br/>月12：年度总结规划]

    classDef planBox fill:#fff8e1,stroke:#f57c00,stroke-width:2px
    classDef phase fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef milestone fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef resource fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef timeline fill:#e3f2fd,stroke:#1565c0,stroke-width:2px

    class L planBox
    class L1,L11,L12,L13 phase
    class L2,L21,L22,L23,L24,L25,L26 milestone
    class L3,L31,L32,L33 resource
    class L4,L41,L42,L43,L44 timeline
            `;
            showDiagram(implDiagram);
        }

        // 页面加载时显示概览
        document.addEventListener('DOMContentLoaded', function() {
            showOverview();
        });
    </script>
</body>
</html>
